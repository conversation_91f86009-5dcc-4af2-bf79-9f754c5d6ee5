/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    can.c
  * @brief   This file provides code for the configuration
  *          of the CAN instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "can.h"
#include "motor.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

CAN_HandleTypeDef hcan1;
CAN_FilterTypeDef can_filter;
CAN_TxHeaderTypeDef Txdata;
CAN_RxHeaderTypeDef Rxdata;

/* CAN1 init function */
void MX_CAN1_Init(void)
{

  /* USER CODE BEGIN CAN1_Init 0 */

  /* USER CODE END CAN1_Init 0 */

  /* USER CODE BEGIN CAN1_Init 1 */

  /* USER CODE END CAN1_Init 1 */
  hcan1.Instance = CAN1;
  hcan1.Init.Prescaler = 6;
  hcan1.Init.Mode = CAN_MODE_NORMAL;
  hcan1.Init.SyncJumpWidth = CAN_SJW_3TQ;
  hcan1.Init.TimeSeg1 = CAN_BS1_2TQ;
  hcan1.Init.TimeSeg2 = CAN_BS2_1TQ;
  hcan1.Init.TimeTriggeredMode = DISABLE;
  hcan1.Init.AutoBusOff = DISABLE;
  hcan1.Init.AutoWakeUp = DISABLE;
  hcan1.Init.AutoRetransmission = DISABLE;
  hcan1.Init.ReceiveFifoLocked = DISABLE;
  hcan1.Init.TransmitFifoPriority = DISABLE;
  if (HAL_CAN_Init(&hcan1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN CAN1_Init 2 */

  /* USER CODE END CAN1_Init 2 */

}

void HAL_CAN_MspInit(CAN_HandleTypeDef* canHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspInit 0 */

  /* USER CODE END CAN1_MspInit 0 */
    /* CAN1 clock enable */
    __HAL_RCC_CAN1_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_11|GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF9_CAN1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /* USER CODE BEGIN CAN1_MspInit 1 */

  /* USER CODE END CAN1_MspInit 1 */
  }
}

void CAN_FILTER(void ){

	can_filter.FilterBank=0;
	can_filter.FilterMode=	CAN_FILTERMODE_IDMASK;
	can_filter.FilterScale=CAN_FILTERSCALE_32BIT;
	can_filter.FilterFIFOAssignment=CAN_FilterFIFO0;
	can_filter.FilterIdHigh=0X0000;
	can_filter.FilterIdLow = 0X0000;
	can_filter.FilterActivation=ENABLE;  
	can_filter.FilterMaskIdHigh=0x0000;
	can_filter.FilterMaskIdLow=0x0000;

	HAL_CAN_ConfigFilter(&hcan1,&can_filter);
}

uint8_t CAN_Txdata(uint8_t*Tdata, uint16_t len, uint32_t id)
{
	uint32_t pTxMailBox_num=0;
	HAL_StatusTypeDef res;

	
	Txdata.StdId = id;           
	Txdata.ExtId = 0x12345;      
	Txdata.RTR = CAN_RTR_DATA;   
	Txdata.IDE = CAN_ID_STD;     
	Txdata.DLC = len;            

	
	res = HAL_CAN_AddTxMessage(&hcan1, &Txdata, Tdata, &pTxMailBox_num);

	if(res == HAL_OK)
	{
		return 1; 
	}
	else
	{
		return 0; 
	}
}

uint8_t CAN_Rxdata(uint8_t* RxData, uint32_t* id, uint8_t* len)
{
	HAL_StatusTypeDef res;

	
	if(HAL_CAN_GetRxFifoFillLevel(&hcan1, CAN_RX_FIFO0) > 0)
	{
		
		res = HAL_CAN_GetRxMessage(&hcan1, CAN_RX_FIFO0, &Rxdata, RxData);

		if(res == HAL_OK)
		{
			
			if(Rxdata.IDE == CAN_ID_STD)
			{
				*id = Rxdata.StdId;  
			}
			else
			{
				*id = Rxdata.ExtId;  
			}
			*len = Rxdata.DLC;       
			return 1; 
		}
	}
	return 0; 
}

// 
uint8_t CAN_Start(void)
{
	
	CAN_FILTER();

	
	if(HAL_CAN_Start(&hcan1) != HAL_OK)
	{
		return 0; 
	}

	
	if(HAL_CAN_ActivateNotification(&hcan1, CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)
	{
		return 0; 
	}

	return 1; 
}


void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan)
{
	uint8_t RxData[8];
	uint32_t id;
	uint8_t len;
	uint8_t i;
	extern motor_measure moto1[];

	if(hcan->Instance == CAN1)
	{
		
		if(CAN_Rxdata(RxData, &id, &len) == 1)
		{
			
			i = id - 0x201;
			switch(id){
				case 0x201:
				
					if (moto1[i].message_count <= 50)
					    motor_offseting(&moto1[i], RxData);
					else
						motor_measureing(&moto1[i], RxData);
					break;
				default:break;
			}
		}
	}
}
	



void HAL_CAN_MspDeInit(CAN_HandleTypeDef* canHandle)
{

  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspDeInit 0 */

  /* USER CODE END CAN1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_CAN1_CLK_DISABLE();

    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_11|GPIO_PIN_12);

  /* USER CODE BEGIN CAN1_MspDeInit 1 */
    HAL_NVIC_DisableIRQ(CAN1_RX0_IRQn);
  /* USER CODE END CAN1_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */
