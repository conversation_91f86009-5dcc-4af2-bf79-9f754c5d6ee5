/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "can.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "../MDK-ARM/motor.h"
#include "../MDK-ARM/pid.h"
#include <stdio.h>
#include <math.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// 电机数据数组 (修正变量名)
motor_measure moto1[motor_num] = {0};

// 系统控制变量
uint8_t system_init_flag = 0;
uint32_t control_counter = 0;
uint32_t led_toggle_time = 0;

// 测试控制变量
float target_speed = 0;      // 目标速度 (RPM)
float target_position = 0;   // 目标位置 (度)
uint8_t control_mode = VELOCITY_CONTROL_MODE;  // 控制模式
uint8_t test_step = 0;       // 测试步骤

// 定时器句柄 (如果使用定时器)
TIM_HandleTypeDef htim6;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void MX_TIM6_Init(void);
void System_Init(void);
void Motor_Control_Task(void);
void Motor_Test_Task(void);
void LED_Task(void);
void Print_Motor_Status(void);
float Motor_Get_Position_Degrees(uint8_t motor_id);
float Motor_Get_Speed_RPM(uint8_t motor_id);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

// 重定向printf到UART1
int fputc(int ch, FILE *f)
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_CAN1_Init();
  MX_TIM6_Init();
  MX_USART1_UART_Init();
  /* USER CODE BEGIN 2 */

  // 系统初始化
  System_Init();

  printf("M3508 Motor Control System Started!\r\n");
  printf("Waiting for motor data...\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // 电机控制任务 (每10ms执行一次，100Hz控制频率)
    static uint32_t control_time = 0;
    if (HAL_GetTick() - control_time >= 10)
    {
        control_time = HAL_GetTick();
        Motor_Control_Task();
    }

    // LED指示任务 (系统运行指示)
    LED_Task();

    // 电机测试任务 (自动测试序列)
    Motor_Test_Task();

    // 打印电机状态 (每1秒一次)
    static uint32_t print_time = 0;
    if (HAL_GetTick() - print_time > 1000)
    {
        print_time = HAL_GetTick();
        Print_Motor_Status();
    }

    // 短暂延时，避免CPU占用过高
    HAL_Delay(1);

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 16;
  RCC_OscInitStruct.PLL.PLLN = 192;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, 2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief 简化的定时器初始化 (使用SysTick)
 */
void MX_TIM6_Init(void)
{
    // 使用SysTick作为控制定时器，无需额外配置
    // SysTick已经在HAL_Init()中初始化为1ms
    printf("Using SysTick for motor control timing.\r\n");
}

/**
 * @brief 系统初始化
 */
void System_Init(void)
{
    // 启动CAN通信
    if (CAN_Start() == 1)
    {
        printf("CAN communication started successfully.\r\n");
    }
    else
    {
        printf("CAN communication start failed!\r\n");
    }

    // 初始化级联PID控制器
    CascadePID_Init(&motor_cascade_pid[0]);

    // 设置默认为速度控制模式
    CascadePID_SetControlMode(&motor_cascade_pid[0], VELOCITY_CONTROL_MODE);

    // 使用主循环进行控制，无需定时器中断
    printf("Motor control will run in main loop.\r\n");

    system_init_flag = 1;
    printf("System initialization completed.\r\n");
}

/**
 * @brief 电机控制任务 (在定时器中断中调用)
 */
void Motor_Control_Task(void)
{
    if (!system_init_flag) return;

    // 检查电机数据是否有效 (收到足够的消息)
    if (moto1[0].message_count > 50)
    {
        // 获取电机反馈数据
        float current_position = Motor_Get_Position_Degrees(0);
        float current_speed = Motor_Get_Speed_RPM(0);
        float current_current = (float)moto1[0].current_real;

        // 执行级联PID控制
        float control_output = 0;

        if (control_mode == POSITION_CONTROL_MODE)
        {
            // 位置控制模式
            control_output = CascadePID_Calc(&motor_cascade_pid[0],
                                           target_position,
                                           current_position,
                                           current_speed,
                                           current_current);
        }
        else if (control_mode == VELOCITY_CONTROL_MODE)
        {
            // 速度控制模式
            control_output = CascadePID_Calc(&motor_cascade_pid[0],
                                           target_speed,
                                           current_position,
                                           current_speed,
                                           current_current);
        }

        // 发送电机控制指令
        int16_t motor_current = (int16_t)control_output;
        motor_current_set(&hcan1, 0x200, motor_current);
    }

    control_counter++;
}

/**
 * @brief 电机测试任务 (自动测试序列)
 */
void Motor_Test_Task(void)
{
    static uint32_t test_time = 0;

    // 每5秒切换一次测试模式
    if (HAL_GetTick() - test_time > 5000)
    {
        test_time = HAL_GetTick();
        test_step++;

        switch (test_step % 6)
        {
            case 0:
                // 停止电机
                target_speed = 0;
                target_position = 0;
                control_mode = VELOCITY_CONTROL_MODE;
                printf("Test Step 0: Motor Stop\r\n");
                break;

            case 1:
                // 速度控制 - 正转100 RPM
                target_speed = 100.0f;
                control_mode = VELOCITY_CONTROL_MODE;
                CascadePID_SetControlMode(&motor_cascade_pid[0], VELOCITY_CONTROL_MODE);
                printf("Test Step 1: Speed Control +100 RPM\r\n");
                break;

            case 2:
                // 速度控制 - 反转100 RPM
                target_speed = -100.0f;
                control_mode = VELOCITY_CONTROL_MODE;
                printf("Test Step 2: Speed Control -100 RPM\r\n");
                break;

            case 3:
                // 位置控制 - 转到90度
                target_position = 90.0f;
                control_mode = POSITION_CONTROL_MODE;
                CascadePID_SetControlMode(&motor_cascade_pid[0], POSITION_CONTROL_MODE);
                printf("Test Step 3: Position Control 90 degrees\r\n");
                break;

            case 4:
                // 位置控制 - 转到-90度
                target_position = -90.0f;
                control_mode = POSITION_CONTROL_MODE;
                printf("Test Step 4: Position Control -90 degrees\r\n");
                break;

            case 5:
                // 位置控制 - 回到0度
                target_position = 0.0f;
                control_mode = POSITION_CONTROL_MODE;
                printf("Test Step 5: Position Control 0 degrees\r\n");
                break;
        }
    }
}

/**
 * @brief LED指示任务
 */
void LED_Task(void)
{
    // 每500ms切换一次LED状态，表示系统正常运行
    if (HAL_GetTick() - led_toggle_time > 500)
    {
        led_toggle_time = HAL_GetTick();
        // 这里可以添加LED控制代码，例如：
        // HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    }
}

/**
 * @brief 打印电机状态
 */
void Print_Motor_Status(void)
{
    if (!system_init_flag) return;

    printf("=== Motor Status ===\r\n");
    printf("Message Count: %lu\r\n", moto1[0].message_count);

    if (moto1[0].message_count > 50)
    {
        printf("Position: %.2f deg\r\n", Motor_Get_Position_Degrees(0));
        printf("Speed: %.1f RPM\r\n", Motor_Get_Speed_RPM(0));
        printf("Current: %d mA\r\n", moto1[0].current_real);
        printf("Temperature: %d C\r\n", moto1[0].hall_state);
        printf("Control Mode: %s\r\n",
               (control_mode == VELOCITY_CONTROL_MODE) ? "Speed" : "Position");
        printf("Target: %.1f %s\r\n",
               (control_mode == VELOCITY_CONTROL_MODE) ? target_speed : target_position,
               (control_mode == VELOCITY_CONTROL_MODE) ? "RPM" : "deg");
    }
    else
    {
        printf("Waiting for motor data... (%lu/50)\r\n", moto1[0].message_count);
    }
    printf("Control Counter: %lu\r\n", control_counter);
    printf("==================\r\n");
}

/**
 * @brief 获取电机位置 (度)
 */
float Motor_Get_Position_Degrees(uint8_t motor_id)
{
    if (motor_id >= motor_num) return 0.0f;

    // 将角度值转换为度数 (8192 = 360度)
    return (float)moto1[motor_id].angle_total * 360.0f / 8192.0f;
}

/**
 * @brief 获取电机速度 (RPM)
 */
float Motor_Get_Speed_RPM(uint8_t motor_id)
{
    if (motor_id >= motor_num) return 0.0f;

    // 注意：这里需要根据实际的数据格式调整
    // 当前代码中speed_zhuansu实际存储的是current_real
    // 需要修正电机数据解析
    return (float)moto1[motor_id].speed_zhuansu;
}

// 定时器相关函数已移除，使用主循环控制

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
