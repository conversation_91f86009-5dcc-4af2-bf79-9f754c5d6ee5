--cpu=Cortex-M4.fp.sp
"m3508_2\startup_stm32f407xx.o"
"m3508_2\main.o"
"m3508_2\gpio.o"
"m3508_2\can.o"
"m3508_2\usart.o"
"m3508_2\stm32f4xx_it.o"
"m3508_2\stm32f4xx_hal_msp.o"
"m3508_2\motor.o"
"m3508_2\pid.o"
"m3508_2\stm32f4xx_hal_can.o"
"m3508_2\stm32f4xx_hal_rcc.o"
"m3508_2\stm32f4xx_hal_rcc_ex.o"
"m3508_2\stm32f4xx_hal_flash.o"
"m3508_2\stm32f4xx_hal_flash_ex.o"
"m3508_2\stm32f4xx_hal_flash_ramfunc.o"
"m3508_2\stm32f4xx_hal_gpio.o"
"m3508_2\stm32f4xx_hal_dma_ex.o"
"m3508_2\stm32f4xx_hal_dma.o"
"m3508_2\stm32f4xx_hal_pwr.o"
"m3508_2\stm32f4xx_hal_pwr_ex.o"
"m3508_2\stm32f4xx_hal_cortex.o"
"m3508_2\stm32f4xx_hal.o"
"m3508_2\stm32f4xx_hal_exti.o"
"m3508_2\stm32f4xx_hal_uart.o"
"m3508_2\system_stm32f4xx.o"
--strict --scatter "m3508_2\m3508_2.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "m3508_2.map" -o m3508_2\m3508_2.axf