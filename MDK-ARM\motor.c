#include "can.h"
#include "motor.h"
#include "stm32f407xx.h"
#include <stdint.h>

motor_measure motor_squence[motor_num]={0};
motor_measure	motor_info;

uint8_t i;

uint8_t motor_measureing(motor_measure*ptr,uint8_t*data){
	ptr->angle_last=ptr->angle_right;
	ptr->angle_right=(uint16_t)(data[0]<<8|data[1]);
	ptr->speed_zhuansu=(int16_t)(data[2]<<8|data[3]);  // 修正：这是转速，不是电流
	ptr->current_real=(int16_t)(data[4]<<8|data[5]);   // 修正：这是实际电流
	ptr->hall_state=data[6];  // 温度
	if(ptr->angle_right-ptr->angle_last>4096)
			ptr->round_offset--;
	else if (ptr->angle_right-ptr->angle_last<-4096)
			ptr->round_offset++;
	ptr->angle_total=ptr->round_offset*8192+ptr->angle_right-ptr->angle_offset;
	ptr->message_count++;  // 增加消息计数
	return 8;
}

void motor_offseting(motor_measure *ptr, uint8_t *data)
{
	ptr->angle_right = (uint16_t)(data[0]<<8 | data[1]);
	ptr->angle_offset = ptr->angle_right;
}

uint8_t motor_current_set(CAN_HandleTypeDef*hcan,int16_t SID,int16_t iq1){

extern CAN_TxHeaderTypeDef Txdata;
	uint8_t Txdatafasong[8];
	uint32_t ptxmailbox;
	
	
	Txdata.StdId=SID;
	Txdata.IDE=CAN_ID_STD;
	Txdata.RTR=CAN_RTR_DATA;
	Txdata.DLC=8;
  Txdatafasong[0]=iq1>>8;
	Txdatafasong[1]=iq1;
	
	if(HAL_CAN_AddTxMessage(hcan,&Txdata,Txdatafasong,&ptxmailbox)!=HAL_OK)
		return 1;
	return 0;



}














