#ifndef __MOTOR_H
#define __MOTOR_H

#include "stm32f407xx.h"
#include "can.h"

typedef struct{
	uint16_t  speed_zhuansu;
	uint16_t	current_real;
	uint16_t	current_goal;
	uint8_t		hall_state;
	uint16_t	angle_right;
	uint16_t	angle_last;
	uint16_t	angle_offset;
	uint32_t	round_offset;
	uint32_t	angle_total;
	uint8_t		buff_id;
	uint16_t	angle_buff[0];
	uint16_t	angle_fited;
	uint32_t	message_count;
}motor_measure;

#define motor_num 1
#define CAN_CONTROL
	
extern motor_measure motor_squene[];
extern motor_measure motor_info;

uint8_t motor_measureing(motor_measure*ptr,uint8_t*data);
void angle_offseting(motor_measure*ptr,uint8_t*data);
uint8_t motor_current_set(CAN_HandleTypeDef*hcan,int16_t SID,int16_t iq1);

#endif