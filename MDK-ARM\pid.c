#include "pid.h"
#include <math.h>
#include <string.h>
#include <stdint.h>

#define ABS(x)		((x>0)? (x): (-x))

// ??????
CascadePID_t motor_cascade_pid[4];  // 4??????PID???

void abs_limit(float *a, float ABS_MAX){
    if(*a > ABS_MAX)
        *a = ABS_MAX;
    if(*a < -ABS_MAX)
        *a = -ABS_MAX;
}

/**
  * @brief  ?PID??????
  * @param  pid: PID?????
  * @param  kp: ????
  * @param  ki: ????
  * @param  kd: ????
  * @param  maxOutput: ??????
  * @param  integralLimit: ????
  * @retval None
  */
void PID_Init(PID_t *pid, float kp, float ki, float kd, float maxOutput, float integralLimit)
{
    memset(pid, 0, sizeof(PID_t));

    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->maxOutput = maxOutput;
    pid->integralLimit = integralLimit;
    pid->enable = 1;
}

/**
  * @brief  ?PID????
  * @param  pid: PID?????
  * @param  targetValue: ???
  * @param  feedbackValue: ???
  * @retval PID???
  */
float PID_Calc(PID_t *pid, float targetValue, float feedbackValue)
{
    if (!pid->enable) {
        return 0;
    }

    // ?????????
    pid->target = targetValue;
    pid->feedback = feedbackValue;

    // ????
    pid->lastError = pid->error;
    pid->error = targetValue - feedbackValue;

    // ???
    float pout = pid->kp * pid->error;

    // ???
    pid->integral += pid->error;
    abs_limit(&(pid->integral), pid->integralLimit);
    float iout = pid->ki * pid->integral;

    // ???
    pid->derivative = pid->error - pid->lastError;
    float dout = pid->kd * pid->derivative;

    // ???
    pid->output = pout + iout + dout;
    abs_limit(&(pid->output), pid->maxOutput);

    return pid->output;
}

/**
  * @brief  ??PID???
  * @param  pid: PID?????
  * @retval None
  */
void PID_Reset(PID_t *pid)
{
    pid->error = 0;
    pid->lastError = 0;
    pid->integral = 0;
    pid->derivative = 0;
    pid->output = 0;
}

/**
  * @brief  ??PID??
  * @param  pid: PID?????
  * @param  kp: ????
  * @param  ki: ????
  * @param  kd: ????
  * @retval None
  */
void PID_SetParams(PID_t *pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
}

/**
  * @brief  ??PID??????
  * @param  cascade_pid: ??PID?????
  * @retval None
  */
void CascadePID_Init(CascadePID_t *cascade_pid)
{
    memset(cascade_pid, 0, sizeof(CascadePID_t));

    // ???????
    // ???PID?? (??,????,?????)
    PID_Init(&cascade_pid->position_pid, 2.0f, 0.05f, 0.1f, 3000.0f, 1000.0f);

    // ???PID?? (??,????????)
    PID_Init(&cascade_pid->velocity_pid, 1.5f, 0.1f, 0.0f, 16384.0f, 8000.0f);

    // ???PID?? (??,????)
    PID_Init(&cascade_pid->current_pid, 10.0f, 1.0f, 0.0f, 16384.0f, 5000.0f);

    cascade_pid->control_mode = VELOCITY_CONTROL_MODE;  // ????????
    cascade_pid->enable = 1;
}

/**
  * @brief  ?????PID??
  * @param  cascade_pid: ??PID?????
  * @param  kp: ????
  * @param  ki: ????
  * @param  kd: ????
  * @param  maxOutput: ????
  * @param  integralLimit: ????
  * @retval None*/
 void CascadePID_SetPositionParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit)
{
    PID_Init(&cascade_pid->position_pid, kp, ki, kd, maxOutput, integralLimit);
}

/**
  * @brief  ?????PID??
  * @param  cascade_pid: ??PID?????
  * @param  kp: ????
  * @param  ki: ????
  * @param  kd: ????
  * @param  maxOutput: ????
  * @param  integralLimit: ????
  * @retval None
  */
void CascadePID_SetVelocityParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit)
{
    PID_Init(&cascade_pid->velocity_pid, kp, ki, kd, maxOutput, integralLimit);
}

/**
  * @brief  ?????PID??
  * @param  cascade_pid: ??PID?????
  * @param  kp: ????
  * @param  ki: ????
  * @param  kd: ????
  * @param  maxOutput: ????
  * @param  integralLimit: ????
  * @retval None
  */
void CascadePID_SetCurrentParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit)
{
    PID_Init(&cascade_pid->current_pid, kp, ki, kd, maxOutput, integralLimit);
}

/**
  * @brief  ??PID????
  * @param  cascade_pid: ??PID?????
  * @param  target_pos: ???? (?)
  * @param  feedback_pos: ???? (?)
  * @param  feedback_vel: ???? (rpm)
  * @param  feedback_cur: ???? (mA)
  * @retval ?????
  */
float CascadePID_Calc(CascadePID_t *cascade_pid, float target_pos, float feedback_pos, float feedback_vel, float feedback_cur)
{
    if (!cascade_pid->enable) {
        return 0;
    }

    // ?????
    cascade_pid->feedback_position = feedback_pos;
    cascade_pid->feedback_velocity = feedback_vel;
    cascade_pid->feedback_current = feedback_cur;

    float final_output = 0;

    switch (cascade_pid->control_mode) {
        case POSITION_CONTROL_MODE:
            // ??????:??? ? ??? ? ???
            cascade_pid->target_position = target_pos;

            // ?????,??????????
            cascade_pid->target_velocity = PID_Calc(&cascade_pid->position_pid,
                                                   cascade_pid->target_position,
                                                   cascade_pid->feedback_position);

            // ?????,??????????
            cascade_pid->target_current = PID_Calc(&cascade_pid->velocity_pid,
                                                  cascade_pid->target_velocity,
                                                  cascade_pid->feedback_velocity);

            // ?????,???????
            final_output = PID_Calc(&cascade_pid->current_pid,
                                   cascade_pid->target_current,
                                   cascade_pid->feedback_current);
            break;

        case VELOCITY_CONTROL_MODE:
            // ??????:??? ? ???
            cascade_pid->target_velocity = target_pos;  // ??target_pos???????

            // ?????,??????????
            cascade_pid->target_current = PID_Calc(&cascade_pid->velocity_pid,
                                                  cascade_pid->target_velocity,
                                                  cascade_pid->feedback_velocity);

            // ?????,???????
            final_output = PID_Calc(&cascade_pid->current_pid,
                                   cascade_pid->target_current,
                                   cascade_pid->feedback_current);
            break;

        case CURRENT_CONTROL_MODE:
            // ????:????
            cascade_pid->target_current = target_pos;  // ??target_pos???????

            // ?????,???????
            final_output = PID_Calc(&cascade_pid->current_pid,
                                   cascade_pid->target_current,
                                   cascade_pid->feedback_current);
            break;

        default:
            final_output = 0;
            break;
    }

    return final_output;
}

/**
  * @brief  ??????
  * @param  cascade_pid: ??PID?????
  * @param  mode: ???? (0=??, 1=??, 2=??)
  * @retval None
  */
void CascadePID_SetControlMode(CascadePID_t *cascade_pid, uint8_t mode)
{
    if (mode <= CURRENT_CONTROL_MODE) {
        cascade_pid->control_mode = mode;
        // ?????????PID
        CascadePID_Reset(cascade_pid);
    }
}

/**
  * @brief  ????PID???
  * @param  cascade_pid: ??PID?????
  * @retval None
  */
void CascadePID_Reset(CascadePID_t *cascade_pid)
{
    PID_Reset(&cascade_pid->position_pid);
    PID_Reset(&cascade_pid->velocity_pid);
    PID_Reset(&cascade_pid->current_pid);

    cascade_pid->target_position = 0;
    cascade_pid->target_velocity = 0;
    cascade_pid->target_current = 0;
}