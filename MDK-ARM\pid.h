#ifndef __MOTOR_PID_H
#define __MOTOR_PID_H
#include "stm32f4xx_hal.h"

// ??PID?????
typedef struct {
    float kp;           // ????
    float ki;           // ????
    float kd;           // ????
    float maxOutput;    // ??????
    float integralLimit;// ????

    float target;       // ???
    float feedback;     // ???
    float error;        // ????
    float lastError;    // ????
    float integral;     // ????
    float derivative;   // ???
    float output;       // PID??

    uint8_t enable;     // PID????
} PID_t;

// ??PID??????
typedef struct {
    PID_t position_pid; // ???PID
    PID_t velocity_pid; // ???PID
    PID_t current_pid;  // ???PID

    // ???????
    float target_position;    // ???? (?)
    float target_velocity;    // ???? (rpm)
    float target_current;     // ???? (mA)

    float feedback_position;  // ???? (?)
    float feedback_velocity;  // ???? (rpm)
    float feedback_current;   // ???? (mA)

    // ????
    uint8_t control_mode;     // 0=????, 1=????, 2=????
    uint8_t enable;           // ??????
} CascadePID_t;

// ??????
#define POSITION_CONTROL_MODE  0
#define VELOCITY_CONTROL_MODE  1
#define CURRENT_CONTROL_MODE   2

// ???PID??????,???????PID??

// ?PID????
void PID_Init(PID_t *pid, float kp, float ki, float kd, float maxOutput, float integralLimit);
float PID_Calc(PID_t *pid, float targetValue, float feedbackValue);
void PID_Reset(PID_t *pid);
void PID_SetParams(PID_t *pid, float kp, float ki, float kd);

// ??PID????
void CascadePID_Init(CascadePID_t *cascade_pid);
void CascadePID_SetPositionParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit);
void CascadePID_SetVelocityParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit);
void CascadePID_SetCurrentParams(CascadePID_t *cascade_pid, float kp, float ki, float kd, float maxOutput, float integralLimit);
float CascadePID_Calc(CascadePID_t *cascade_pid, float target_pos, float feedback_pos, float feedback_vel, float feedback_cur);
void CascadePID_SetControlMode(CascadePID_t *cascade_pid, uint8_t mode);
void CascadePID_Reset(CascadePID_t *cascade_pid);

// ??????
extern CascadePID_t motor_cascade_pid[4];  // 4??????PID???
#endif
