# M3508电机CAN通信PID控制系统

## 项目简介
基于STM32F407VGZ6的M3508电机CAN通信PID控制系统，支持速度控制和位置控制。

## 硬件连接
- **CAN通信**: PA11(RX), PA12(TX)
- **调试串口**: UART1 (115200波特率)
- **电机**: M3508无刷电机 (ID: 0x201)

## 主要功能
1. **CAN通信**: 自动接收电机反馈数据
2. **级联PID控制**: 位置环+速度环+电流环
3. **自动测试**: 6步测试序列，每5秒切换一次
4. **实时监控**: 串口输出电机状态

## 测试序列
- **步骤0**: 电机停止
- **步骤1**: 速度控制 +100 RPM
- **步骤2**: 速度控制 -100 RPM  
- **步骤3**: 位置控制 90度
- **步骤4**: 位置控制 -90度
- **步骤5**: 位置控制 0度

## 使用方法
1. 连接硬件（CAN总线、电机、调试串口）
2. 编译并下载程序到STM32F407
3. 打开串口调试助手（115200波特率）
4. 观察系统启动信息和电机状态
5. 系统会自动执行测试序列

## 控制参数调节
在`pid.c`的`CascadePID_Init()`函数中可以调节PID参数：
```c
// 位置PID参数
PID_Init(&cascade_pid->position_pid, 2.0f, 0.05f, 0.1f, 3000.0f, 1000.0f);
// 速度PID参数  
PID_Init(&cascade_pid->velocity_pid, 1.5f, 0.1f, 0.0f, 16384.0f, 8000.0f);
```

## 自定义控制
可以在主函数中修改测试序列，或者通过串口发送指令来控制电机：
```c
// 设置速度控制模式
control_mode = VELOCITY_CONTROL_MODE;
target_speed = 200.0f;  // 目标速度200 RPM

// 设置位置控制模式
control_mode = POSITION_CONTROL_MODE;
target_position = 180.0f;  // 目标位置180度
```

## 注意事项
1. 确保CAN总线正确连接和终端电阻配置
2. 电机ID必须设置为0x201
3. 建议先在无负载情况下测试
4. 根据实际电机特性调整PID参数
